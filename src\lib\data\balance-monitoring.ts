/**
 * 电网监控系统模拟数据
 */

import type { ChartData, SectionTableData, InfoDisplayData, MarkerData } from '$lib/types/balance-monitoring';

// 时间轴数据（小时）
const timeAxis: string[] = [
  "00",
  "02",
  "04",
  "06",
  "08",
  "10",
  "12",
  "14",
  "16",
  "18",
  "20",
  "22",
];

// 平衡曲线数据
export const balanceData: ChartData = {
  title: "平衡曲线",
  unit: "单位:万千瓦",
  xAxis: [
    "08-09 00",
    "02",
    "04",
    "06",
    "08",
    "10",
    "12",
    "14",
    "16",
    "18",
    "20",
    "22",
    "08-10 00",
  ],
  series: [
    {
      name: "全网可用最大",
      colorType: "green",
      data: [
        11500, 11000, 10800, 11200, 11800, 12500, 13000, 13581, 13200, 12800,
        12206, 11800, 11500,
      ],
      currentValue: { value: 13581, position: 7 },
    },
    {
      name: "全网可用最小",
      colorType: "blue",
      data: [
        7500, 7000, 6800, 7200, 7800, 8500, 9200, 10000, 10579, 9800, 9000,
        8500, 8000,
      ],
      currentValue: { value: 10579, position: 8 },
    },
    {
      name: "全网用电",
      colorType: "yellow",
      data: [
        10000, 9500, 9150, 9800, 10500, 11200, 11800, 12003, 11500, 11000,
        10500, 10200, 10000,
      ],
      currentValue: { value: 12003, position: 7 },
    },
  ],
  markers: [
    {
      value: 1253,
      text: "负备用最小值:1253",
      position: { x: 6, y: 6000 },
      colorType: "blue",
    },
    {
      value: 781,
      text: "正备用最小值:781",
      position: { x: 7, y: 12800 },
      colorType: "green",
    },
  ],
};

// 风电消纳曲线数据
export const windData: ChartData = {
  title: "风电消纳曲线",
  unit: "单位:万千瓦",
  xAxis: timeAxis,
  series: [
    {
      name: "风电",
      colorType: "green",
      data: [600, 650, 680, 700, 690, 680, 672.9, 650, 630, 610, 600, 590],
      currentValue: { value: 672.9, position: 6 },
    },
    {
      name: "风电消纳(含深调能力)",
      colorType: "blue",
      data: [
        1200, 1300, 1350, 1400, 1380, 1422.8, 1400, 1380, 1350, 1320, 1300,
        1280,
      ],
      currentValue: { value: 1422.8, position: 5 },
    },
  ],
  yAxis: {
    min: 200,
    max: 2500,
  },
};

// 光伏消纳曲线数据
export const solarData: ChartData = {
  title: "光伏消纳曲线",
  unit: "单位:万千瓦",
  xAxis: timeAxis,
  series: [
    {
      name: "统调光伏",
      colorType: "green",
      data: [0, 0, 0, 50, 120, 180, 211.4, 200, 150, 80, 20, 0],
      currentValue: { value: 211.4, position: 6 },
    },
    {
      name: "统调光伏消纳",
      colorType: "blue",
      data: [0, 0, 0, 100, 250, 380, 495.2, 450, 350, 200, 50, 0],
      currentValue: { value: 495.2, position: 6 },
    },
  ],
  yAxis: {
    min: 0,
    max: 600,
  },
};

// 断面监视表格数据
export const sectionData: SectionTableData = {
  title: "断面监视",
  columns: ["断面名称", "限额", "预测潮流", "差额", "越限时间"],
  data: [
    ["XXX 断面名称1", "XXX", "XXX 预测潮流1", "XXX", "2025-06-13 04:00"],
    ["XXX 断面名称2", "XXX", "XXX 预测潮流2", "XXX", "2025-06-13 04:00"],
    ["XXX 断面名称3", "XXX", "XXX 预测潮流3", "XXX", "2025-06-13 04:00"],
    ["XXX 断面名称4", "XXX", "XXX 预测潮流4", "XXX", "2025-06-13 04:00"],
    ["XXX 断面名称5", "XXX", "XXX 预测潮流5", "XXX", "2025-06-13 04:00"],
  ],
};

// 当班费用和实时ACE数据
export const infoData: InfoDisplayData = {
  currentCost: "XXXX",
  realTimeACE: "XXXX",
};

// 导出所有数据
export { timeAxis };
