<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { buildRealtimeDataUrl } from '$lib/config/balance-monitoring/chart-config';
	import type { InfoDisplayData, ApiResponse } from '$lib/types/balance-monitoring';

	// Props
	export let containerId: string = 'info-container';
	export let data: InfoDisplayData | null = null;

	// 组件状态
	let container: HTMLElement;
	let displayData: InfoDisplayData | null = data;
	let apiUrl: string = buildRealtimeDataUrl(['REAL_TIME_ACE', 'CURRENT_COST']);

	onMount(() => {
		init();
	});

	function init() {
		if (!container) {
			console.error('容器元素不存在');
			return;
		}

		// 如果没有传入数据，则从接口获取
		if (!displayData) {
			fetchData();
		} else {
			render();
		}
	}

	// 从接口获取数据
	async function fetchData() {
		try {
			const response = await fetch(apiUrl);
			const result: ApiResponse<number[]> = await response.json();

			if (result.code === '0000' && result.data && Array.isArray(result.data)) {
				// 转换接口数据为组件格式
				displayData = transformApiData(result.data);
				render();
			} else {
				console.error('接口返回错误:', result);
			}
		} catch (error) {
			console.error('接口调用失败:', error);
		}
	}

	// 转换接口数据为组件格式
	function transformApiData(apiData: number[]): InfoDisplayData {
		// 根据API文档，返回数据按参数顺序排列
		// 第一个参数是实时ACE (130010:320000000000010034)
		// 第二个参数是当班费用 (130037:320000000000010010)
		return {
			realTimeACE: formatNumber(apiData[0]),
			currentCost: formatNumber(apiData[1])
		};
	}

	// 格式化数字为1位小数
	function formatNumber(value: any): string {
		if (value === null || value === undefined || value === '') {
			return 'N/A';
		}

		const num = parseFloat(value);
		if (isNaN(num)) {
			return value; // 如果不是数字，返回原值
		}

		return num.toFixed(1);
	}

	// 显示错误信息
	function showError(message: string) {
		const costValueElement = container.querySelector(
			'.info-box:nth-child(1) .info-box-value'
		) as HTMLElement;
		const aceValueElement = container.querySelector(
			'.info-box:nth-child(2) .info-box-value'
		) as HTMLElement;

		if (costValueElement && aceValueElement) {
			costValueElement.textContent = message;
			aceValueElement.textContent = message;
		}
	}

	function render() {
		if (!displayData) {
			showError('数据格式错误');
			return;
		}

		// 获取当班费用和实时ACE的容器
		const costValueElement = container.querySelector(
			'.info-box:nth-child(1) .info-box-value'
		) as HTMLElement;
		const aceValueElement = container.querySelector(
			'.info-box:nth-child(2) .info-box-value'
		) as HTMLElement;

		if (costValueElement && aceValueElement) {
			costValueElement.textContent = displayData.currentCost;
			aceValueElement.textContent = displayData.realTimeACE;
		} else {
			console.error('信息显示元素不存在');
		}
	}

	// 更新数据
	export function updateData(newData: InfoDisplayData) {
		displayData = newData;
		render();
	}

	// 刷新数据（重新从接口获取）
	export function refreshData() {
		fetchData();
	}
</script>

<div bind:this={container} id={containerId} class="info-container">
	<div class="info-box current-cost">
		<div class="info-box-value">--</div>
	</div>
	<div class="info-box real-time-ace">
		<div class="info-box-value">--</div>
	</div>
</div>

<style>
	.info-container {
		display: flex;
		flex: 1;
		flex-direction: row;
		align-items: center;
	}

	.info-box {
		text-align: center;
		display: flex;
		flex: 1;
		flex-direction: column;
		align-items: center;
		position: relative;
		height: 5rem;
		background-size: contain;
		background-repeat: no-repeat;
		background-position: 50%;
	}

	.current-cost {
		background-image: url('$lib/assets/balance-monitoring/images/CurrentCost.webp');
	}

	.real-time-ace {
		background-image: url('$lib/assets/balance-monitoring/images/RealTimeACE.webp');
	}

	.current-cost .info-box-value {
		background-image: linear-gradient(
			to bottom,
			#ffffff 0%,
			#d4fafd 30%,
			#9cf6fe 60%,
			#00586a 100%
		); /* 背景渐变 */
		-webkit-background-clip: text; /* 文字渐变 */
		color: transparent; /* 文字透明 */
	}

	.real-time-ace .info-box-value {
		background-image: linear-gradient(
			to bottom,
			#ffffff 0%,
			#e9f0d2 30%,
			#f9da79 60%,
			#543e09 100%
		); /* 背景渐变 */
		-webkit-background-clip: text; /* 文字渐变 */
		color: transparent; /* 文字透明 */
	}

	.info-box-value {
		position: absolute;
		bottom: 14.5%;
		left: 38%;
		font-size: 1.2rem;
		font-weight: bold;
		opacity: 0.8;
	}

	@media screen and (min-width: 2000px) {
		.info-box {
			height: 6rem;
		}
		.info-box-value {
			font-size: 1.5rem;
			left: 41%;
			bottom: 12%;
		}
	}

	@media screen and (min-width: 3000px) {
		.info-box {
			height: 7rem;
		}
		.info-box-value {
			font-size: 1.8rem;
			left: 41%;
			bottom: 10%;
		}
	}

	@media screen and (min-width: 4000px) {
		.info-box-value {
			font-size: 1.8rem;
			left: 39%;
		}
	}
</style>
