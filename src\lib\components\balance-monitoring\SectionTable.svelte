<script lang="ts">
	import { onMount } from 'svelte';
	import { API_CONFIG } from '$lib/config/balance-monitoring/chart-config';
	import type {
		SectionTableData,
		SectionDataItem,
		ApiResponse
	} from '$lib/types/balance-monitoring';

	// Props
	export let containerId: string = 'section-table';
	export let data: SectionTableData | null = null;

	// 组件状态
	let container: HTMLElement;
	let tableData: SectionTableData | null = data;
	let apiUrl: string = `${API_CONFIG.BASE_URL}${API_CONFIG.SECTION_DATA.url}`;

	onMount(() => {
		init();
	});

	function init() {
		if (!container) {
			console.error('容器元素不存在');
			return;
		}

		// 如果没有传入数据，则从接口获取
		if (!tableData) {
			fetchData();
		} else {
			render();
		}
	}

	// 从接口获取数据
	async function fetchData() {
		try {
			const response = await fetch(apiUrl);
			const result: ApiResponse<SectionDataItem[]> = await response.json();

			if (result.code === '0000' && result.data) {
				// 转换接口数据为表格格式
				tableData = transformApiData(result.data);
				render();
			} else {
				console.error('接口返回错误:', result);
			}
		} catch (error) {
			console.error('接口调用失败:', error);
		}
	}

	// 转换接口数据为表格格式
	function transformApiData(apiData: SectionDataItem[]): SectionTableData {
		const columns = ['断面名称', '限额', '预测潮流', '差额'];

		const data = apiData.map((item) => [
			item.sectionName,
			formatNumber(item.limitValue, 0),
			formatNumber(item.predictionValue, 0),
			formatNumber(item.diffValue, 0)
		]);

		return { columns, data };
	}

	// 格式化数字为指定小数位数
	function formatNumber(value: any, decimals: number = 1): string {
		if (value === null || value === undefined || value === '') {
			return ' ';
		}

		const num = parseFloat(value);
		if (isNaN(num)) {
			return value; // 如果不是数字，返回原值
		}

		return num.toFixed(decimals);
	}

	// 显示错误信息
	function showError(message: string) {
		container.innerHTML = `<div class="error-message">${message}</div>`;
	}

	function render() {
		if (!tableData || !tableData.columns || !tableData.data) {
			showError('数据格式错误');
			return;
		}

		const { columns, data } = tableData;

		// 创建表格元素
		const table = document.createElement('table');
		table.className = 'section-table';

		// 创建表头
		const thead = document.createElement('thead');
		const headerRow = document.createElement('tr');

		columns.forEach((column) => {
			const th = document.createElement('th');
			const text = document.createElement('span');
			text.textContent = column;
			th.appendChild(text);
			headerRow.appendChild(th);
		});

		thead.appendChild(headerRow);
		table.appendChild(thead);

		// 创建表格主体
		const tbody = document.createElement('tbody');

		data.forEach((row) => {
			const tr = document.createElement('tr');

			row.forEach((cell) => {
				const td = document.createElement('td');
				td.textContent = cell;
				tr.appendChild(td);
			});

			tbody.appendChild(tr);
		});

		table.appendChild(tbody);

		// 清空容器并添加表格
		container.innerHTML = '';
		container.appendChild(table);
	}

	// 更新数据
	export function updateData(newData: SectionTableData) {
		tableData = newData;
		render();
	}

	// 刷新数据（重新从接口获取）
	export function refreshData() {
		fetchData();
	}
</script>

<div bind:this={container} id={containerId}></div>

<style>
	:global(.section-table) {
		padding: 0 1.5vh;
		width: 100%;
		font-size: 1rem;
		border-collapse: separate;
		border-spacing: 0px 0.6vh;
	}

	:global(.section-table th),
	:global(.section-table td) {
		padding: 1.5vh;
		text-align: center;
	}

	:global(.section-table th) {
		background: linear-gradient(to bottom, #042e58 0%, #011e3b 15%, #003f6b 100%);
		font-weight: bold;
	}

	:global(.section-table th span) {
		background-image: linear-gradient(
			to bottom,
			#ffffff 0%,
			#ddf1fb 30%,
			#a9dcfe 60%,
			#64c8f5 100%
		); /* 背景渐变 */
		-webkit-background-clip: text; /* 文字渐变 */
		color: transparent; /* 文字透明 */
	}

	:global(.section-table td) {
		color: #fff;
		background: linear-gradient(to bottom, #042e5893 0%, #011e3b93 15%, #003f6b93 100%);
	}

	@media screen and (min-width: 2000px) {
		:global(.section-table) {
			font-size: 1.5rem;
		}
	}

	@media screen and (min-width: 3000px) {
		:global(.section-table) {
			font-size: 1.5rem;
		}
	}
</style>
