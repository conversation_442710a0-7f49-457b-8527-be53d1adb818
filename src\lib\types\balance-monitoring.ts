/**
 * Balance Monitoring 模块的 TypeScript 类型定义
 */

// ============= API 相关类型 =============

export interface ApiResponse<T = any> {
	code: string;
	data: T;
	message?: string;
}

export interface ApiConfig {
	BASE_URL: string;
	REALTIME_DATA: {
		url: string;
		DATA_TYPES: {
			REAL_TIME_ACE: string;
			CURRENT_COST: string;
		};
	};
	SECTION_DATA: {
		url: string;
	};
	CPS_DATA: {
		url: string;
	};
	REALTIME_CPS_DATA: {
		url: string;
		DATA_TYPES: {
			CPS1: string;
			CPS2: string;
		};
	};
}

// ============= 图表数据类型 =============

export interface ChartData {
	title?: string;
	unit?: string;
	xAxis: string[];
	series: SeriesData[];
	yAxis?: {
		min?: number;
		max?: number;
		interval?: number;
		showMinorTick?: boolean;
	};
	markers?: MarkerData[];
}

export interface SeriesData {
	name: string;
	colorType: 'green' | 'blue' | 'yellow' | 'red' | 'orange';
	data: (number | null)[];
	currentValue?: {
		value: number;
		position: number;
	};
	showSymbol?: boolean;
	symbolSize?: number;
}

export interface MarkerData {
	value: number;
	text: string;
	position: {
		x: number;
		y: number;
	};
	colorType: 'green' | 'blue' | 'yellow' | 'red' | 'orange';
}

export interface SectionDataItem {
	sectionName: string;
	limitValue: number;
	predictionValue: number;
	diffValue: number;
}

export interface SectionTableData {
	title?: string;
	columns: string[];
	data: string[][];
}

export interface InfoData {
	currentCost: string;
	realTimeACE: string;
}

export interface InfoDisplayData {
	currentCost: string;
	realTimeACE: string;
}

export interface CPSApiResponse {
	code: string;
	data: {
		CPS1List: CPSDataPoint[];
		CPS2List: CPSDataPoint[];
	};
	message?: string;
}

export interface CPSApiData {
	CPS1List: CPSDataPoint[];
	CPS2List: CPSDataPoint[];
}

export interface CPSDataPoint {
	time: string;
	value: number;
}

export interface RealtimeData {
	CPS1: {
		value: number | null;
		time: string;
	} | null;
	CPS2: {
		value: number | null;
		time: string;
	} | null;
}

export interface DateRange {
	startDay: string;
	endDay: string;
}

export interface IframeRefs {
	balanceIframe?: HTMLIFrameElement;
	windIframe?: HTMLIFrameElement;
	solarIframe?: HTMLIFrameElement;
}

export interface ColorOption {
	color: string;
	colorWithWhite: string;
	colorTransparent: string;
	colorTransparent2: string;
}

export interface ColorOptions {
	green: ColorOption;
	blue: ColorOption;
	yellow: ColorOption;
	red?: ColorOption;
	orange?: ColorOption;
}

export interface ChartColors {
	primary: string;
	text: string;
	label: string;
	textSecondary: string;
	textTitle: string;
	background: string;
	backgroundLight: string;
	border: string;
}

export interface TooltipConfig {
	trigger?: string;
	backgroundColor?: string;
	borderColor?: string;
	textStyle?: {
		color?: string;
	};
	formatter?: (params: any) => string;
}

export interface ChartConfig {
	xAxis: string[];
	series: SeriesData[];
	yAxis?: {
		min?: number;
		max?: number;
		interval?: number;
		showMinorTick?: boolean;
	};
	gridConfig?: GridConfig;
	legendConfig?: LegendConfig;
	xAxisConfig?: AxisConfig;
	yAxisConfig?: AxisConfig;
	seriesConfig?: SeriesConfig;
}

// ============= 图表配置类型 =============

export interface GridConfig {
	left?: string;
	right?: string;
	top?: string;
	bottom?: string;
	containLabel?: boolean;
}

export interface LegendConfig {
	textStyle?: {
		color?: string;
		fontSize?: number;
	};
	top?: string;
	right?: string;
	itemHeight?: number;
	itemStyle?: {
		borderWidth?: number;
	};
	data?: Array<{
		name: string;
		itemStyle?: {
			color?: string;
			borderColor?: string;
		};
	}>;
}

export interface AxisConfig {
	type?: string;
	axisLine?: {
		lineStyle?: {
			color?: string;
			width?: number;
		};
	};
	axisLabel?: {
		color?: string;
		fontSize?: number;
	};
	axisTick?: {
		lineStyle?: {
			width?: number;
		};
	};
	splitLine?: {
		show?: boolean;
		lineStyle?: {
			color?: string;
			type?: string;
			width?: number;
		};
	};
	data?: string[];
	name?: string;
	nameTextStyle?: {
		color?: string;
	};
	min?: number | null;
	max?: number | null;
	interval?: number | null;
	minorSplitLine?: {
		show?: boolean;
		lineStyle?: {
			color?: string;
			opacity?: number;
		};
	};
}

export interface SeriesConfig {
	name: string;
	type: string;
	data: (number | null)[];
	smooth?: boolean;
	symbol?: string;
	symbolSize?: number;
	showSymbol?: boolean;
	itemStyle?: {
		color?: string;
		borderWidth?: number;
		borderColor?: string;
	};
	lineStyle?: {
		width?: number;
		color?: string;
		type?: string;
	};
	markPoint?: {
		data?: Array<{
			value: number;
			xAxis: number;
			yAxis: number;
			itemStyle?: {
				color?: string;
				borderWidth?: number;
				borderColor?: string;
			};
		}>;
		symbolSize?: number;
		symbol?: string;
		itemStyle?: {
			color?: string;
			borderWidth?: number;
			borderColor?: string;
		};
		label?: {
			show?: boolean;
			position?: string;
			formatter?: string;
			color?: string;
		};
	};
	colorType?: string;
	row?: any;
}

// ============= 组件实例类型 =============

export interface ComponentInstance {
	container: HTMLElement | null;
	data: any;
	init(): void;
	render(): void;
	updateData(newData: any): void;
	refreshData(): void;
	destroy?(): void;
}

export interface ChartInstance extends ComponentInstance {
	chart: any; // ECharts instance
	apiUrl: string;
	fetchData(): Promise<void>;
	transformApiData(apiData: any): any;
	showError(message: string): void;
}

export interface TimerInstance {
	refreshTimer: NodeJS.Timeout | null;
	refreshInterval: number;
	startAutoRefresh(): void;
	stopAutoRefresh(): void;
	setRefreshInterval(intervalMs: number): void;
}
