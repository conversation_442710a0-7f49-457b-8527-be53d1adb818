<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import dayjs from 'dayjs';
	import * as echarts from 'echarts';
	import {
		processSimpleSeries,
		createChartOption,
		buildCPSDataUrl,
		buildRealtimeCPSDataUrl,
		getDefaultCPSDateRange,
		ColorOptions
	} from '$lib/config/balance-monitoring/chart-config';
	import type {
		ChartData,
		CPSApiData,
		RealtimeData,
		DateRange,
		ApiResponse
	} from '$lib/types/balance-monitoring';

	// Props
	export let containerId: string = 'cps-chart';
	export let data: ChartData | null = null;
	export let dateRange: DateRange | null = null;

	// 组件状态
	let container: HTMLElement;
	let chart: echarts.ECharts | null = null;
	let chartData: ChartData | null = data;
	let currentDateRange: DateRange = dateRange || getDefaultCPSDateRange();
	let apiUrl: string = buildCPSDataUrl(currentDateRange.startDay, currentDateRange.endDay);
	let realtimeApiUrl: string = buildRealtimeCPSDataUrl();
	let refreshTimer: NodeJS.Timeout | null = null;
	let realtimeTimer: NodeJS.Timeout | null = null;
	let realtimeData: RealtimeData = { CPS1: null, CPS2: null };

	// 配置常量
	const refreshInterval = 60000; // 刷新间隔：60秒（1分钟）
	const realtimeInterval = 1000; // 实时数据刷新间隔：1秒
	let resizeHandler: (() => void) | null = null;
	let mediaQueryHandler: (() => void) | null = null;

	onMount(() => {
		initChart();
	});

	onDestroy(() => {
		destroy();
	});

	function initChart() {
		if (!container) {
			console.error('容器元素不存在');
			return;
		}

		// 获取设备像素比，用于高分辨率屏幕适配
		const devicePixelRatio = window.devicePixelRatio || 1;

		// 初始化ECharts实例，配置高分辨率支持
		chart = echarts.init(container, null, {
			devicePixelRatio: devicePixelRatio,
			renderer: 'canvas', // 使用canvas渲染器以获得更好的性能
			useDirtyRect: true // 启用脏矩形优化
		});

		// 如果没有传入数据，则从接口获取
		if (!chartData) {
			fetchData();
		} else {
			chartData = transformApiData(chartData);
			render();
		}

		// 启动定时刷新
		startAutoRefresh();

		// 启动实时数据获取
		startRealtimeDataFetch();

		// 添加窗口大小变化和设备像素比变化的监听器
		setupResizeListeners();
	}

	// 设置响应式监听器
	function setupResizeListeners() {
		// 窗口大小变化监听器
		resizeHandler = () => {
			if (chart) {
				chart.resize();
			}
		};

		// 设备像素比变化监听器（用于检测显示器切换或缩放变化）
		mediaQueryHandler = () => {
			if (chart) {
				// 重新初始化图表以适应新的设备像素比
				const currentDevicePixelRatio = window.devicePixelRatio || 1;
				if (currentDevicePixelRatio !== chart.getDevicePixelRatio()) {
					console.log('设备像素比变化，重新初始化图表');
					reinitializeChart();
				}
			}
		};

		window.addEventListener('resize', resizeHandler);

		// 监听设备像素比变化
		if (window.matchMedia) {
			const mediaQuery = window.matchMedia(`(resolution: ${window.devicePixelRatio}dppx)`);
			if (mediaQuery.addEventListener) {
				mediaQuery.addEventListener('change', mediaQueryHandler);
			} else if (mediaQuery.addListener) {
				// 兼容旧版本浏览器
				mediaQuery.addListener(mediaQueryHandler);
			}
		}
	}

	// 重新初始化图表（用于设备像素比变化时）
	function reinitializeChart() {
		if (!chart) return;

		// 保存当前配置
		const currentOption = chart.getOption();

		// 销毁旧图表
		chart.dispose();

		// 重新创建图表
		const devicePixelRatio = window.devicePixelRatio || 1;
		chart = echarts.init(container, null, {
			devicePixelRatio: devicePixelRatio,
			renderer: 'canvas',
			useDirtyRect: true
		});

		// 恢复配置
		if (currentOption) {
			chart.setOption(currentOption);
		} else if (chartData) {
			render();
		}
	}

	// 从接口获取数据
	async function fetchData() {
		try {
			const response = await fetch(apiUrl);
			const result: ApiResponse<CPSApiData> = await response.json();

			if (result.code === '0000' && result.data) {
				// 转换接口数据为图表格式
				chartData = transformApiData(result.data);
				render();
			} else {
				console.error('CPS接口返回错误:', result);
			}
		} catch (error) {
			console.error('CPS接口调用失败:', error);
		}
	}

	// 获取实时CPS数据
	async function fetchRealtimeData() {
		try {
			const response = await fetch(realtimeApiUrl);
			const result: ApiResponse<number[]> = await response.json();

			if (result.code === '0000' && result.data) {
				processRealtimeData(result.data);
			} else {
				console.error('实时CPS接口返回错误:', result);
			}
		} catch (error) {
			console.error('实时CPS接口调用失败:', error);
		}
	}

	// 处理实时数据
	function processRealtimeData(realtimeApiData: number[]) {
		// 更新实时数据存储
		if (Array.isArray(realtimeApiData)) {
			realtimeData.CPS1 = {
				value: formatNumber(realtimeApiData[0]),
				time: dayjs().format('YYYY-MM-DD HH:mm:ss')
			};
			realtimeData.CPS2 = {
				value: formatNumber(realtimeApiData[1]),
				time: dayjs().format('YYYY-MM-DD HH:mm:ss')
			};

			// 将实时数据插入到历史数据中
			insertRealtimeDataToHistory();

			// 重新渲染图表
			render();

			console.log('实时CPS数据已更新:', realtimeData);
		}
	}

	// 将实时数据插入到历史数据中
	function insertRealtimeDataToHistory() {
		if (!chartData || !chartData.series) return;

		// 使用dayjs生成当前时间字符串，格式与CPS接口返回格式一致
		const timeString = dayjs().format('YYYY-MM-DD HH:mm:ss');
		const displayTime = formatTimeDisplay(timeString);

		// 检查是否需要添加新的时间点
		if (!chartData.xAxis.includes(displayTime)) {
			chartData.xAxis.push(displayTime);

			// 为每个系列添加新的数据点
			chartData.series.forEach((series, index) => {
				if (index === 0 && realtimeData.CPS1) {
					// CPS1系列
					series.data.push(realtimeData.CPS1.value);
				} else if (index === 1 && realtimeData.CPS2) {
					// CPS2系列
					series.data.push(realtimeData.CPS2.value);
				} else {
					// 其他系列添加null值
					series.data.push(null);
				}
			});
		} else {
			// 更新现有时间点的数据
			const timeIndex = chartData.xAxis.indexOf(displayTime);
			if (timeIndex !== -1) {
				chartData.series.forEach((series, index) => {
					if (index === 0 && realtimeData.CPS1) {
						series.data[timeIndex] = realtimeData.CPS1.value;
					} else if (index === 1 && realtimeData.CPS2) {
						series.data[timeIndex] = realtimeData.CPS2.value;
					}
				});
			}
		}
	}

	// 转换接口数据为图表格式
	function transformApiData(apiData: CPSApiData | ChartData): ChartData {
		// 如果已经是 ChartData 格式，直接返回
		if ('xAxis' in apiData && 'series' in apiData) {
			return apiData;
		}

		const { CPS1List = [], CPS2List = [] } = apiData as CPSApiData;

		// 提取所有时间点，去重并排序作为X轴
		const allTimes = [
			...CPS1List.map((item: any) => item.time),
			...CPS2List.map((item: any) => item.time)
		];
		const uniqueTimes = [...new Set(allTimes)].sort();

		// 格式化时间显示，只显示月份和时间，去除年份
		const xAxis = uniqueTimes.map((time) => formatTimeDisplay(time));

		// 创建时间到值的映射
		const timeValueMap1 = new Map();
		const timeValueMap2 = new Map();

		CPS1List.forEach((item: any) => {
			timeValueMap1.set(item.time, formatNumber(item.value));
		});

		CPS2List.forEach((item: any) => {
			timeValueMap2.set(item.time, formatNumber(item.value));
		});

		// 根据X轴时间生成对应的Y轴数据
		const cps1Data = uniqueTimes.map((time) => timeValueMap1.get(time) || null);
		const cps2Data = uniqueTimes.map((time) => timeValueMap2.get(time) || null);

		return {
			xAxis,
			series: [
				{
					name: '当班CPS1',
					colorType: 'green',
					showSymbol: true,
					data: cps1Data
				},
				{
					name: '当班CPS2',
					colorType: 'blue',
					showSymbol: true,
					data: cps2Data
				}
			]
		};
	}

	// 格式化时间显示，只显示月份和时间，去除年份，精确到秒
	function formatTimeDisplay(timeString: string): string {
		// 输入格式: "2025-06-17 00:00:00"
		// 输出格式: "06-17 00:00:00"
		if (!timeString) return timeString;

		try {
			// 使用dayjs解析和格式化时间
			const date = dayjs(timeString);

			// 检查日期是否有效
			if (!date.isValid()) {
				console.warn('无效的时间格式:', timeString);
				return timeString;
			}

			// 返回月-日 时:分:秒格式
			return date.format('MM-DD HH:mm:ss');
		} catch (error) {
			console.warn('时间格式化失败:', timeString, error);
			return timeString;
		}
	}

	// 格式化数字为1位小数
	function formatNumber(value: any): number | null {
		if (value === null || value === undefined || value === '') {
			return null;
		}

		const num = parseFloat(value);
		if (isNaN(num)) {
			return null;
		}

		return parseFloat(num.toFixed(1));
	}

	// 显示错误信息
	function showError(message: string) {
		if (chart) {
			chart.showLoading({
				text: message,
				color: '#c23531',
				textColor: '#c23531',
				maskColor: 'rgba(255, 255, 255, 0.8)',
				zlevel: 0
			});
		}
	}

	// 渲染图表
	function render() {
		if (!chartData) {
			showError('数据格式错误');
			return;
		}

		// 隐藏加载状态
		if (chart) {
			chart.hideLoading();
		}

		const { xAxis, series, yAxis } = chartData;

		// 获取设备像素比用于高分辨率适配
		const devicePixelRatio = window.devicePixelRatio || 1;
		const scaleFactor = Math.max(1, devicePixelRatio * 0.8); // 适度缩放，避免过大

		// 使用公共配置处理简单系列数据
		const seriesData = processSimpleSeries(series);

		// 为高分辨率屏幕调整配置
		const highResConfig = getHighResolutionConfig(scaleFactor);

		// 使用公共配置创建图表选项
		const option = createChartOption({
			xAxis,
			series: seriesData,
			yAxis,
			gridConfig: {
				top: '20%',
				bottom: '15%' // 增加底部空间以容纳旋转的标签
			},
			legendConfig: highResConfig.legend,
			xAxisConfig: {
				...highResConfig.xAxis
			},
			yAxisConfig: highResConfig.yAxis
		});

		chart?.setOption(option);
		drawRealtimeDataView(xAxis, seriesData, scaleFactor);
	}

	// 获取高分辨率屏幕配置
	function getHighResolutionConfig(scaleFactor: number) {
		const baseFontSize = 12;
		const baseLineWidth = 2;
		const baseSymbolSize = 6;

		return {
			legend: {
				textStyle: {
					fontSize: Math.round(baseFontSize * scaleFactor)
				},
				itemHeight: Math.round(8 * scaleFactor),
				itemStyle: {
					borderWidth: Math.max(1, Math.round(baseLineWidth * scaleFactor))
				}
			},
			xAxis: {
				axisLabel: {
					fontSize: Math.round(baseFontSize * scaleFactor)
				},
				axisLine: {
					lineStyle: {
						width: Math.max(1, Math.round(baseLineWidth * scaleFactor * 0.5))
					}
				},
				axisTick: {
					lineStyle: {
						width: Math.max(1, Math.round(baseLineWidth * scaleFactor * 0.5))
					}
				}
			},
			yAxis: {
				axisLabel: {
					fontSize: Math.round(baseFontSize * scaleFactor)
				},
				axisLine: {
					lineStyle: {
						width: Math.max(1, Math.round(baseLineWidth * scaleFactor * 0.5))
					}
				},
				axisTick: {
					lineStyle: {
						width: Math.max(1, Math.round(baseLineWidth * scaleFactor * 0.5))
					}
				},
				splitLine: {
					lineStyle: {
						width: Math.max(1, Math.round(baseLineWidth * scaleFactor * 0.3))
					}
				}
			},
			series: {
				lineStyle: {
					width: Math.max(2, Math.round(baseLineWidth * scaleFactor))
				},
				symbolSize: Math.max(4, Math.round(baseSymbolSize * scaleFactor))
			}
		};
	}

	// 绘制实时数据视图
	function drawRealtimeDataView(data: string[], seriesData: any[], scaleFactor: number = 1) {
		// === 计算最后一个点的位置（单位：像素） ===
		const lastIndex = data.length - 1;
		const lastCategory = data[lastIndex];
		const lastCPS1Value = seriesData[0].data[lastIndex];
		const lastCPS2Value = seriesData[1].data[lastIndex];

		// 等待图表渲染完成后再计算像素坐标
		setTimeout(() => {
			if (!chart) return;

			const pos1 = chart.convertToPixel({ seriesIndex: 0 }, [lastCategory, lastCPS1Value]);
			const pos2 = chart.convertToPixel({ seriesIndex: 1 }, [lastCategory, lastCPS2Value]);

			// 获取图表容器的尺寸
			const chartHeight = chart.getHeight();
			// const chartWidth = chart.getWidth();

			// Group配置 - 根据缩放因子调整
			const baseGroupWidth = 130;
			const baseGroupHeight = 20;
			const baseFontSize = 12;
			const baseLineWidth = 2;
			const baseMinDistance = 30;
			const baseMarginFromEdge = 50;

			const groupWidth = Math.round(baseGroupWidth * scaleFactor);
			const groupHeight = Math.round(baseGroupHeight * scaleFactor);
			const fontSize = Math.round(baseFontSize * scaleFactor);
			const lineWidth = Math.max(1, Math.round(baseLineWidth * scaleFactor));
			const minDistance = Math.round(baseMinDistance * scaleFactor);
			const marginFromEdge = Math.round(baseMarginFromEdge * scaleFactor);

			// 计算group的最佳位置，避免重叠
			const positions = calculateGroupPositions(
				pos1,
				pos2,
				chartHeight,
				groupHeight,
				minDistance,
				marginFromEdge
			);

			// 计算group中心点坐标（用于箭头起始点）
			// const group1Center = {
			// 	x: chartWidth - 10 - groupWidth / 2,
			// 	y: positions.group1Top + groupHeight / 2
			// };

			// const group2Center = {
			// 	x: chartWidth - 10 - groupWidth / 2,
			// 	y: positions.group2Top + groupHeight / 2
			// };

			chart?.setOption({
				graphic: {
					elements: [
						// CPS1 实时值显示组
						{
							type: 'group',
							right: 10,
							top: positions.group1Top,
							children: [
								// 背景矩形
								{
									type: 'rect',
									z: 1000,
									shape: {
										width: groupWidth,
										height: groupHeight,
										r: 3
									},
									style: {
										fill: ColorOptions.green.colorTransparent2,
										stroke: ColorOptions.green.color,
										lineWidth: lineWidth,
										shadowBlur: Math.round(8 * scaleFactor),
										shadowColor: 'rgba(0,0,0,0.3)'
									}
								},
								// 文本
								{
									type: 'text',
									z: 1001,
									left: 10 * scaleFactor,
									top: fontSize / 2,
									style: {
										text: `CPS1实时值: ${lastCPS1Value}`,
										fill: ColorOptions.green.color,
										font: `${fontSize}px sans-serif`,
										textAlign: 'left',
										textVerticalAlign: 'top'
									}
								}
							]
						}
					]
				}
			});
		}, 100);
	}

	// 计算group的最佳位置，避免重叠
	function calculateGroupPositions(
		pos1: number[],
		pos2: number[],
		chartHeight: number,
		groupHeight: number,
		minDistance: number,
		marginFromEdge: number
	) {
		// 根据数据点的Y坐标确定group的初始位置
		let group1Top: number, group2Top: number;

		// 计算数据点在图表中的相对位置
		const pos1Ratio = pos1[1] / chartHeight;
		const pos2Ratio = pos2[1] / chartHeight;

		// 根据数据点位置智能确定group位置
		if (pos1Ratio < 0.3) {
			// 如果CPS1点在上部，group放在下方
			group1Top = pos1[1] + 30;
		} else if (pos1Ratio > 0.7) {
			// 如果CPS1点在下部，group放在上方
			group1Top = pos1[1] - groupHeight - 30;
		} else {
			// 如果CPS1点在中部，根据具体情况调整
			group1Top = chartHeight * 0.1;
		}

		if (pos2Ratio < 0.3) {
			// 如果CPS2点在上部，group放在下方
			group2Top = pos2[1] + 30;
		} else if (pos2Ratio > 0.7) {
			// 如果CPS2点在下部，group放在上方
			group2Top = pos2[1] - groupHeight - 30;
		} else {
			// 如果CPS2点在中部，根据具体情况调整
			group2Top = chartHeight * 0.7;
		}

		// 确保group不超出图表边界
		group1Top = Math.max(
			marginFromEdge,
			Math.min(group1Top, chartHeight - groupHeight - marginFromEdge)
		);
		group2Top = Math.max(
			marginFromEdge,
			Math.min(group2Top, chartHeight - groupHeight - marginFromEdge)
		);

		// 检查并解决重叠问题
		const distance = Math.abs(group1Top - group2Top);
		if (distance < minDistance) {
			// 如果重叠，调整位置
			const midPoint = (group1Top + group2Top) / 2;
			const halfDistance = minDistance / 2;

			if (group1Top < group2Top) {
				group1Top = midPoint - halfDistance - groupHeight / 2;
				group2Top = midPoint + halfDistance - groupHeight / 2;
			} else {
				group2Top = midPoint - halfDistance - groupHeight / 2;
				group1Top = midPoint + halfDistance - groupHeight / 2;
			}

			// 再次确保不超出边界
			group1Top = Math.max(
				marginFromEdge,
				Math.min(group1Top, chartHeight - groupHeight - marginFromEdge)
			);
			group2Top = Math.max(
				marginFromEdge,
				Math.min(group2Top, chartHeight - groupHeight - marginFromEdge)
			);
		}

		return {
			group1Top,
			group2Top
		};
	}

	// 启动自动刷新
	function startAutoRefresh() {
		// 清除现有定时器
		stopAutoRefresh();

		// 设置新的定时器
		refreshTimer = setInterval(() => {
			console.log('CPS图表自动刷新数据...');
			fetchData();
		}, refreshInterval);

		console.log(`CPS图表自动刷新已启动，间隔：${refreshInterval / 1000}秒`);
	}

	// 停止自动刷新
	function stopAutoRefresh() {
		if (refreshTimer) {
			clearInterval(refreshTimer);
			refreshTimer = null;
			console.log('CPS图表自动刷新已停止');
		}
	}

	// 启动实时数据获取
	function startRealtimeDataFetch() {
		// 清除现有定时器
		stopRealtimeDataFetch();

		// 立即获取一次实时数据
		fetchRealtimeData();

		// 设置定时器
		realtimeTimer = setInterval(() => {
			console.log('获取实时CPS数据...');
			fetchRealtimeData();
		}, realtimeInterval);

		console.log(`实时CPS数据获取已启动，间隔：${realtimeInterval / 1000}秒`);
	}

	// 停止实时数据获取
	function stopRealtimeDataFetch() {
		if (realtimeTimer) {
			clearInterval(realtimeTimer);
			realtimeTimer = null;
			console.log('实时CPS数据获取已停止');
		}
	}

	// 销毁图表
	function destroy() {
		// 停止自动刷新
		stopAutoRefresh();

		// 停止实时数据获取
		stopRealtimeDataFetch();

		// 移除事件监听器
		if (resizeHandler) {
			window.removeEventListener('resize', resizeHandler);
		}

		if (chart) {
			chart.dispose();
			chart = null;
		}
	}

	// 更新数据
	export function updateData(newData: ChartData) {
		chartData = newData;
		render();
	}

	// 更新日期范围并重新获取数据
	export function updateDateRange(startDay: string, endDay: string) {
		currentDateRange = { startDay, endDay };
		apiUrl = buildCPSDataUrl(startDay, endDay);
		fetchData();
	}

	// 刷新数据（重新从接口获取）
	export function refreshData() {
		fetchData();
	}
</script>

<div bind:this={container} id={containerId} class="cps-chart-container"></div>

<style>
	.cps-chart-container {
		width: 100%;
		height: 100%;
		min-height: 300px;
	}
</style>
