<script lang="ts">
	import { onMount } from 'svelte';
	import { browser } from '$app/environment';
	import CPSChart from '$lib/components/balance-monitoring/CPSChart.svelte';
	import InfoDisplay from '$lib/components/balance-monitoring/InfoDisplay.svelte';
	import SectionTable from '$lib/components/balance-monitoring/SectionTable.svelte';
	import type { IframeRefs } from '$lib/types/balance-monitoring';

	// 组件引用
	let cpsChart: CPSChart;
	let infoDisplay: InfoDisplay;
	let sectionTable: SectionTable;

	// iframe 引用
	let iframeRefs: IframeRefs = {};

	onMount(() => {
		if (browser) {
			setupIframeUrls();
		}
	});

	/**
	 * 设置三个iframe的地址
	 * 从环境变量中获取URL并设置到对应的iframe
	 */
	function setupIframeUrls() {
		// 获取环境变量中的URL
		const productionUrl =
			'http://***********:8080/clouddddp/#/balanceNewTable?userld=3847091120e111e88818fa163e2609bc';
		const mainUrl = import.meta.env.DEV ? '/test' : productionUrl;

		if (!mainUrl) return;

		// 设置平衡图表iframe的src属性
		if (iframeRefs.balanceIframe) {
			iframeRefs.balanceIframe.src = mainUrl;
			console.log('平衡图表iframe地址已设置:', mainUrl);
		} else {
			console.warn('平衡图表iframe未找到', {
				iframe: !!iframeRefs.balanceIframe,
				url: mainUrl
			});
		}

		// 设置风电图表iframe的src属性
		if (iframeRefs.windIframe) {
			iframeRefs.windIframe.src = mainUrl;
			console.log('风电图表iframe地址已设置:', mainUrl);
		} else {
			console.warn('风电图表iframe未找到', {
				iframe: !!iframeRefs.windIframe,
				url: mainUrl
			});
		}

		// 设置光伏图表iframe的src属性
		if (iframeRefs.solarIframe) {
			iframeRefs.solarIframe.src = mainUrl;
			console.log('光伏图表iframe地址已设置:', mainUrl);
		} else {
			console.warn('光伏图表iframe未找到', {
				iframe: !!iframeRefs.solarIframe,
				url: mainUrl
			});
		}
	}
</script>

<svelte:head>
	<title>全网平衡监视</title>
	<link rel="stylesheet" href="/src/lib/assets/balance-monitoring/styles/balance-monitoring.css" />
</svelte:head>

<div class="container">
	<main class="main-grid">
		<header class="header"></header>
		<div class="charts-row top-layout">
			<div class="chart-container balance-chart-layout">
				<div class="chart-title"></div>
				<div class="balance-chart-container">
					<iframe
						bind:this={iframeRefs.balanceIframe}
						frameborder="0"
						marginheight="0"
						marginwidth="0"
						scrolling="no"
						referrerpolicy="no-referrer"
						title="平衡图表"
					></iframe>
				</div>
			</div>
			<div class="charts-column">
				<div class="chart-container wind-chart-layout">
					<div class="chart-title"></div>
					<div class="wind-chart-container">
						<iframe
							bind:this={iframeRefs.windIframe}
							frameborder="0"
							marginheight="0"
							marginwidth="0"
							scrolling="no"
							referrerpolicy="no-referrer"
							title="风电图表"
						></iframe>
					</div>
				</div>
				<div class="chart-container solar-chat-layout">
					<div class="chart-title"></div>
					<div class="solar-chart-container">
						<iframe
							bind:this={iframeRefs.solarIframe}
							frameborder="0"
							marginheight="0"
							marginwidth="0"
							scrolling="no"
							referrerpolicy="no-referrer"
							title="光伏图表"
						></iframe>
					</div>
				</div>
			</div>
		</div>
		<div class="charts-row bottom-layout">
			<div class="table-container section-table-layout">
				<div class="chart-title"></div>
				<SectionTable bind:this={sectionTable} containerId="section-table" />
			</div>
			<div class="charts-column right-column">
				<div class="chart-container">
					<CPSChart bind:this={cpsChart} containerId="cps-chart" />
					<InfoDisplay bind:this={infoDisplay} containerId="info-container" />
				</div>
			</div>
		</div>
	</main>
</div>

<style>
	/* 页面特定样式可以在这里添加 */
	:global(body) {
		margin: 0;
		padding: 0;
		background-color: #020f22;
		color: #c0c0c0;
	}
</style>
